package com.shouqianba.trade.fund.settlement.api.request;

import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAccountModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAmountModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementFlowBizInfoModel;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementTradeInfoModel;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@ToString
@SuperBuilder(toBuilder = true)
@Jacksonized
public class SettlementFlowCreateRequest extends BaseRequest {

    /**
     * 流水类型:1-入账结算 2-分账结算
     */
    @NotNull(message = "流水类型不能为空")
    private Byte type;

    /**
     * 交易单号
     */
    @NotNull(message = "交易单号不能为空")
    private String transSn;

    /**
     * 业务订单号
     */
    @NotNull(message = "业务订单号不能为空")
    private String orderSn;

    /**
     * 批次号
     */
    @NotNull(message = "批次号不能为空")
    private Long batchId;

    /**
     * 资金池ID
     */
//    @NotNull(message = "资金池ID不能为空")
    private Long poolId;

    /**
     * 付款方信息
     */
    @Valid
    @NotNull(message = "付款方信息不能为空")
    private FundSettlementAccountModel fromInfo;

    /**
     * 收款方信息
     */
    @Valid
    @NotNull(message = "收款方信息不能为空")
    private FundSettlementAccountModel toInfo;

    /**
     * 金额信息
     */
    @Valid
    @NotNull(message = "金额信息不能为空")
    private FundSettlementAmountModel amount;

    /**
     * 交易信息
     */
    @Valid
    @NotNull(message = "交易信息不能为空")
    private FundSettlementTradeInfoModel tradeInfo;

    /**
     * 业务信息
     */
    @Valid
    private FundSettlementFlowBizInfoModel bizInfo;
}