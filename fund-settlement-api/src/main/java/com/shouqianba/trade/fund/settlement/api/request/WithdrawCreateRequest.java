package com.shouqianba.trade.fund.settlement.api.request;

import lombok.Getter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/***
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@ToString
@SuperBuilder(toBuilder = true)
@Jacksonized
public class WithdrawCreateRequest extends BaseRequest {

    /**
     * 客户端流水号，代表唯一键
     */
    @NotEmpty(message = "客户端流水号不能为空")
    private String clientSn;

    /**
     * 提现金额（单位：分）
     * 当商户提现指定金额时上传
     */
    private Long amount;

    /**
     * 是否提现所有金额
     * 当商户提现所有时上传
     */
    private Boolean withdrawAllAmount;

    /**
     * 提现的商户ID
     */
    @NotEmpty(message = "商户ID不能为空")
    private String merchantId;

    /**
     * 品牌编号
     */
    @NotEmpty(message = "品牌编号不能为空")
    private String brandSn;

    /**
     * 商户编号
     */
    @NotEmpty(message = "商户编号不能为空")
    private String merchantSn;

    /**
     * 收单公司
     */
    @NotNull(message = "收单公司不能为空")
    private Integer acquiringCompany;

    /**
     * 统计时间
     */
    @NotEmpty(message = "统计时间不能为空")
    private String statisticsDate;

    /**
     * 验证提现金额和是否提现所有金额不能同时为空
     */
    @AssertTrue(message = "提现金额和是否提现所有金额不能同时为空")
    public boolean isWithdrawAmountValid() {
        return Objects.nonNull(amount) || Objects.nonNull(withdrawAllAmount);
    }
}