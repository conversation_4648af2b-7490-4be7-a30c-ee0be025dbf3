package com.shouqianba.trade.fund.settlement.api.request.model;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@ToString
@Jacksonized
@Builder(toBuilder = true)
public class FundSettlementAmountModel {

    @NotNull(message = "原始金额不能为空")
    private Long originAmount;

    private Long fee;

    // todo 完善验证逻辑
    private Long settleAmount;

}
