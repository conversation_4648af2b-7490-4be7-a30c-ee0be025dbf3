package com.shouqianba.trade.fund.settlement.api.request.model;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@ToString
@Jacksonized
@Builder(toBuilder = true)
public class FundSettlementBatchBizInfoModel {

    @NotNull(message = "结算类型不能为空")
    private Byte settleType; // 结算类型：1-结算、2-收款、3-退款、4-分账、5-分账回退

    @NotNull(message = "收单机构不能为空")
    private Integer acquiringCompany;

}
