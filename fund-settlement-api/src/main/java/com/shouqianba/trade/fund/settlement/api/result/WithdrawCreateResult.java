package com.shouqianba.trade.fund.settlement.api.result;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Getter;

/***
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
@Builder
public class WithdrawCreateResult {

    /**
     * 提现单号
     */
    @JsonProperty("withdraw_sn")
    private String withdrawSn;

    /**
     * 提现唯一键
     */
    private String clientSn;

    /**
     * 提现状态：1-处理中 2-成功 3-失败
     */
    private Integer status;
    
    /**
     * 提现金额（单位：分）
     */
    private Long amount;

}