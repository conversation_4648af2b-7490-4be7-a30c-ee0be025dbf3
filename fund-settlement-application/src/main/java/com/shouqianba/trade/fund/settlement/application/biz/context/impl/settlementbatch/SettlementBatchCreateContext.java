
package com.shouqianba.trade.fund.settlement.application.biz.context.impl.settlementbatch;

import com.shouqianba.trade.fund.settlement.api.request.SettlementBatchCreateRequest;
import com.shouqianba.trade.fund.settlement.api.request.model.FundSettlementAccountModel;
import com.shouqianba.trade.fund.settlement.api.result.SettlementBatchCreateResult;
import com.shouqianba.trade.fund.settlement.application.biz.context.BaseContext;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandDetailInfoGetResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandbusiness.model.res.BrandMerchantInfoQueryResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.coreb.model.res.StoreInfoQueryResult;
import com.shouqianba.trade.fund.settlement.infrastructure.support.DefaultSerialGenerator;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRootFactory;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.enums.BatchTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.enums.SettlementBatchStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo.SettlementBatchAccountInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo.SettlementBatchAmountVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo.SettlementBatchBizInfoVO;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.AccountTypeEnum;
import lombok.Getter;

import java.util.Optional;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
public class SettlementBatchCreateContext extends BaseContext {

    private final SettlementBatchCreateRequest request;
    private final AccountInfo fromAccount = new AccountInfo();
    private final AccountInfo toAccount = new AccountInfo();

    private SettlementBatchAggrRoot settlementBatchAggrRoot;

    @Getter
    private static class AccountInfo {
        private BrandDetailInfoGetResult brandDetailInfo;
        private BrandMerchantInfoQueryResult brandMerchantInfo;
        private StoreInfoQueryResult storeInfo;
    }

    private SettlementBatchCreateContext(SettlementBatchCreateRequest request) {
        this.request = request;
    }

    public static SettlementBatchCreateContext newInstance(SettlementBatchCreateRequest request) {
        return new SettlementBatchCreateContext(request);
    }

    public void bindAccountInfo(
            boolean isFrom, BrandDetailInfoGetResult brandDetailInfo,
            BrandMerchantInfoQueryResult brandMerchantInfo, StoreInfoQueryResult storeInfo) {
        AccountInfo targetAccount = isFrom ? fromAccount : toAccount;
        targetAccount.brandDetailInfo = brandDetailInfo;
        targetAccount.brandMerchantInfo = brandMerchantInfo;
        targetAccount.storeInfo = storeInfo;
    }

    public SettlementBatchAggrRoot genSettlementBatchAggrRoot() {
        FundSettlementAccountModel fromInfo = request.getFromInfo();
        FundSettlementAccountModel toInfo = request.getToInfo();

        return SettlementBatchAggrRootFactory
                .builder()
                .coreBuilder()
                .id(DefaultSerialGenerator
                        .getInstance()
                        .genFundSettlementBatchId())
                .type(BatchTypeEnum.ofCode(request.getType()))
                .fromType(AccountTypeEnum.ofCode(fromInfo.getType()))
                .fromBrandSn(fromInfo.getBrandSn())
                .fromMerchantSn(fromInfo.getMerchantSn())
                .fromStoreSn(fromInfo.getStoreSn())
                .fromInfo(buildAccountInfo(fromInfo, fromAccount))
                .toType(AccountTypeEnum.ofCode(toInfo.getType()))
                .toBrandSn(toInfo.getBrandSn())
                .toMerchantSn(toInfo.getMerchantSn())
                .toStoreSn(toInfo.getStoreSn())
                .toInfo(buildAccountInfo(toInfo, toAccount))
                .amount(SettlementBatchAmountVO.genFromJsonObject(request.getAmount(),
                        SettlementBatchAmountVO.class))
                .bizInfo(SettlementBatchBizInfoVO
                        .builder()
                        .acquiringCompany(request
                                .getBizInfo()
                                .getAcquiringCompany())
                        .build())
                .status(SettlementBatchStatusEnum.ENTRY)
                .build();
    }

    private SettlementBatchAccountInfoVO buildAccountInfo(FundSettlementAccountModel info, AccountInfo accountInfo) {
        return SettlementBatchAccountInfoVO
                .builder()
                .brandId(accountInfo
                        .getBrandMerchantInfo()
                        .getBrandId())
                .brandSn(accountInfo
                        .getBrandMerchantInfo()
                        .getBrandSn())
                .storeId(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getStoreId)
                        .orElse(null))
                .storeSn(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getStoreSn)
                        .orElse(null))
                .fundMerchantId(accountInfo
                        .getBrandMerchantInfo()
                        .getMerchantId())
                .fundMerchantSn(accountInfo
                        .getBrandDetailInfo()
                        .getMerchantSn())
                .sqbMerchantId(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getMerchantId)
                        .orElse(null))
                .sqbMerchantSn(Optional
                        .ofNullable(accountInfo.getStoreInfo())
                        .map(StoreInfoQueryResult::getMerchantSn)
                        .orElse(null))
                .channelMerchantSn(info.getChannelMerchantSn())
                .build();
    }

    public void bindSettlementBatchAggrRoot(SettlementBatchAggrRoot root) {
        this.settlementBatchAggrRoot = root;
    }

    public SettlementBatchCreateResult genResult() {
        return SettlementBatchCreateResult
                .builder()
                .batchId(settlementBatchAggrRoot.getId())
                .build();
    }
}
