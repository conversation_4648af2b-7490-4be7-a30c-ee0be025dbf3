package com.shouqianba.trade.fund.settlement.application.biz.context.impl.withdraw;

import com.shouqianba.trade.fund.settlement.api.request.WithdrawCreateRequest;
import com.shouqianba.trade.fund.settlement.api.result.WithdrawCreateResult;
import com.shouqianba.trade.fund.settlement.application.biz.context.BaseContext;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.req.SettlementWithdrawSubmitRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementWithdrawSubmitResult;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.query.SettlementStatisticsAggrQuery;
import lombok.Getter;

import java.time.LocalDate;
import java.util.Objects;

/**
 * 提现创建上下文
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Getter
public class WithdrawCreateContext extends BaseContext {

    /**
     * 提现创建请求
     */
    private final WithdrawCreateRequest request;

    /**
     * 提现结果
     */
    private SettlementWithdrawSubmitResult withdrawSubmitResult;

    private WithdrawCreateContext(WithdrawCreateRequest request) {
        this.request = request;
    }

    /**
     * 创建实例
     *
     * @param request 提现创建请求
     * @return 提现创建上下文
     */
    public static WithdrawCreateContext newInstance(WithdrawCreateRequest request) {
        return new WithdrawCreateContext(request);
    }

    /**
     * 生成提现提交请求
     *
     * @return 提现提交请求
     */
    public SettlementWithdrawSubmitRequest genWithdrawSubmitRequest() {
        return SettlementWithdrawSubmitRequest.builder()
                .clientSn(request.getClientSn())
                .amount(request.getAmount())
                .withdrawAllAmount(request.getWithdrawAllAmount())
                .merchantId(request.getMerchantId())
                .build();
    }

    /**
     * 绑定提现结果
     *
     * @param withdrawSubmitResult 提现结果
     */
    public void bindWithdrawSubmitResult(SettlementWithdrawSubmitResult withdrawSubmitResult) {
        this.withdrawSubmitResult = withdrawSubmitResult;
    }

    /**
     * 生成结果
     *
     * @return 提现创建结果
     */
    public WithdrawCreateResult genResult() {
        if (Objects.isNull(withdrawSubmitResult)) {
            return WithdrawCreateResult.builder().build();
        }
        return WithdrawCreateResult.builder()
                .withdrawSn(withdrawSubmitResult.getWithdrawSn())
                .status(withdrawSubmitResult.getStatus())
                .amount(withdrawSubmitResult.getAmount())
                .clientSn(withdrawSubmitResult.getClientSn())
                .build();
    }

    public SettlementStatisticsAggrQuery genClearingStatisticsQuery() {
        return SettlementStatisticsAggrQuery.builder()
                .type((byte) 1) // 清分统计类型
                .brandSn(request.getBrandSn())
                .merchantSn(request.getMerchantSn())
                .acquiringCompany(request.getAcquiringCompany())
                .statisticsDate(LocalDate.parse(request.getStatisticsDate()))
                .build();
    }

    public SettlementStatisticsAggrQuery genSplitStatisticsQuery() {
        return SettlementStatisticsAggrQuery.builder()
                .type((byte) 2) // 分账统计类型
                .brandSn(request.getBrandSn())
                .merchantSn(request.getMerchantSn())
                .acquiringCompany(request.getAcquiringCompany())
                .statisticsDate(LocalDate.parse(request.getStatisticsDate()))
                .build();
    }

    public boolean isWithdrawAllAmount() {
        return Objects.equals(request.getWithdrawAllAmount(), Boolean.TRUE);
    }
}