package com.shouqianba.trade.fund.settlement.application.biz.impl.handler.impl;

import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.BaseEventStrategy;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.context.impl.BatchEntrySettlementEventContext;
import com.shouqianba.trade.fund.settlement.application.biz.impl.handler.holder.EventStrategyHolder;
import com.shouqianba.trade.fund.settlement.application.biz.model.FileInfo;
import com.shouqianba.trade.fund.settlement.application.biz.service.CsvExportService;
import com.shouqianba.trade.fund.settlement.application.biz.service.SettlementFlowQueryService;
import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.BrandSettleClient;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementClearingDetailResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementClearingSubmitResult;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.oss.SettlementOssClient;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.EventAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.BatchClearingSettlementStatusEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.event.model.enums.EventTypeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.SettlementBatchDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.SettlementBatchAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.SettlementFlowDomainRepository;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR> Date: 2025/6/14 Time: 16:20 PM
 */
@Slf4j
@Component
public class BatchEntrySettlementStrategy extends EventStrategyHolder<BatchEntrySettlementEventContext> {

    private static final EventTypeEnum EVENT_TYPE = EventTypeEnum.BATCH_ENTRY_SETTLE;
    private static final String RESULT_SUCCESS = "批次入账结算成功";
    private static final String RESULT_FAILURE = "批次入账结算失败";
    private static final int DEFAULT_PAGE_SIZE = 100;

    @Resource
    private BrandSettleClient brandSettleClient;

    @Resource
    private SettlementBatchDomainRepository settlementBatchDomainRepository;
    @Resource
    private SettlementFlowDomainRepository settlementFlowDomainRepository;
    @Resource
    private CsvExportService csvExportService;
    @Resource
    private SettlementFlowQueryService settlementFlowQueryService;
    @Resource
    private SettlementOssClient settlementOssClient;
    @Value("${aliyun.oss.bucket-name}")
    private String bucketName;

    @Override
    protected Pair<EventTypeEnum, BaseEventStrategy<BatchEntrySettlementEventContext>> register() {
        return Pair.of(EVENT_TYPE, this);
    }

    @Override
    public void execute(BatchEntrySettlementEventContext context) {
        EventAggrRoot eventAggrRoot = context.getEventAggrRoot();
        context.bindBucketName(bucketName);

        SettlementBatchAggrRoot settlementBatchAggrRoot =
                settlementBatchDomainRepository.query(context.genSettlementBatchAggrQuery());
        settlementBatchAggrRoot.checkExist();
        context.bindSettlementBatchAggrRoot(settlementBatchAggrRoot);


        if (settlementBatchAggrRoot.isNeedEntrySettle()) {

            // 生成CSV文件并获取文件信息
            FileInfo fileInfo = generateCsvFile(settlementBatchAggrRoot.getId(), settlementBatchAggrRoot);
            context.bindFileInfo(fileInfo);

            log.info("[批次入账结算]CSV文件生成完成，文件名: {}, 本地路径: {}, OSS路径: {}",
                    fileInfo.getFileName(), fileInfo.getLocalFilePath(), fileInfo.getOssFilePath());

            // 上传文件到OSS
            settlementOssClient.upload(context.genOssUploadRequest(
                    fileInfo.getFileName(),
                    fileInfo.getOssFilePath(),
                    fileInfo.getFileContent()));
            log.info("[批次入账结算]文件上传OSS完成，文件名: {}", fileInfo.getFileName());

            // 提交清算请求给第三方
            SettlementClearingSubmitResult clearingSubmitResult =
                    brandSettleClient.submitClearing(context.genSettlementClearingSubmitRequest(
                            fileInfo.getFileName(),
                            fileInfo.getOssFilePath()));
            context.bindClearingSubmitResult(clearingSubmitResult);

            if (clearingSubmitResult.isSuccess()) {
                settlementBatchAggrRoot.updateEntrySettleStatus(BatchClearingSettlementStatusEnum.PROCESSING);
                log.info("[批次入账结算]清算请求提交成功，批次ID: {}, 文件名: {}",
                        settlementBatchAggrRoot.getId(), fileInfo.getFileName());
                return;
            }

            context.updSettlementBatchAggrRootClearingSubmitResult();

            log.error("[批次入账结算]清算请求提交失败，批次ID: {}, 文件名: {}",
                    settlementBatchAggrRoot.getId(), fileInfo.getFileName());
            return;
        }

        SettlementClearingDetailResult settlementClearingDetailResult =
                brandSettleClient.queryClearingDetail(context.genSettlementClearingDetailRequest());
        if (settlementClearingDetailResult.isSuccess()) {
            settlementBatchAggrRoot.updateEntrySettleStatus(BatchClearingSettlementStatusEnum.PROCESSED);
            eventAggrRoot.processSuccess(RESULT_SUCCESS);
            return;
        }
        settlementBatchAggrRoot.updateEntrySettleStatus(BatchClearingSettlementStatusEnum.FAILED);
        eventAggrRoot.processFailure(RESULT_FAILURE);

    }

    /**
     * 生成CSV文件并返回完整的文件信息
     *
     * @param batchId 批次ID
     * @param settlementBatchAggrRoot 结算批次聚合根
     * @return 文件信息对象
     */
    private FileInfo generateCsvFile(Long batchId, SettlementBatchAggrRoot settlementBatchAggrRoot) {
        String localFilePath = null;
        try {
            log.info("[批次入账结算]开始生成CSV文件，批次ID: {}", batchId);

            // 创建数据提供者
            java.util.function.Function<Integer, List<SettlementFlowAggrRoot>> dataProvider =
                    settlementFlowQueryService.createDataProvider(batchId, FlowTypeEnum.ENTRY_SETTLEMENT);

            // 生成基础文件名（不含扩展名和时间戳）
            String baseFileName = "batch_entry_settlement_" + batchId;

            // 导出CSV文件，获取本地文件路径
            localFilePath = csvExportService.exportSettlementFlowToCsv(dataProvider, baseFileName);

            // 从本地文件路径提取实际文件名
            String actualFileName = FileInfo.extractFileName(localFilePath);

            // 生成OSS文件路径
            String ossFilePath = settlementBatchAggrRoot.genFilePath(actualFileName);

            // 读取文件内容
            byte[] fileContent = readFileContent(localFilePath);

            // 构建文件信息对象
            FileInfo fileInfo = FileInfo.builder()
                    .fileName(actualFileName)
                    .localFilePath(localFilePath)
                    .ossFilePath(ossFilePath)
                    .fileContent(fileContent)
                    .build();

            log.info("[批次入账结算]CSV文件生成成功，批次ID: {}, 文件名: {}, 文件大小: {} bytes",
                    batchId, actualFileName, fileInfo.getFileSize());

            return fileInfo;

        } catch (Exception e) {
            log.error("[批次入账结算]CSV文件生成失败，批次ID: {}, 错误信息: {}", batchId, e.getMessage(), e);
            // 异常时清理可能生成的文件
            cleanupLocalFileOnError(localFilePath);
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SERVER_ERROR,
                    "CSV文件生成失败: " + e.getMessage());
        }
    }

    /**
     * 读取文件内容
     *
     * @param filePath 文件路径
     * @return 文件内容字节数组
     */
    private byte[] readFileContent(String filePath) {
        try {
            File file = new File(filePath);
            if (!file.exists()) {
                throw new IllegalArgumentException("文件不存在: " + filePath);
            }
            return FileUtils.readFileToByteArray(file);
        } catch (IOException e) {
            log.error("[批次入账结算]读取文件内容失败，文件路径: {}, 错误信息: {}", filePath, e.getMessage(), e);
            throw new RuntimeException("读取文件内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 错误时清理本地文件
     *
     * @param filePath 文件路径
     */
    private void cleanupLocalFileOnError(String filePath) {
        if (filePath == null) {
            return;
        }
        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("[批次入账结算]已清理错误文件: {}", filePath);
                } else {
                    log.warn("[批次入账结算]清理错误文件失败: {}", filePath);
                }
            }
        } catch (Exception e) {
            log.warn("[批次入账结算]清理错误文件时发生异常，文件路径: {}, 异常信息: {}", filePath, e.getMessage());
        }
    }

}
