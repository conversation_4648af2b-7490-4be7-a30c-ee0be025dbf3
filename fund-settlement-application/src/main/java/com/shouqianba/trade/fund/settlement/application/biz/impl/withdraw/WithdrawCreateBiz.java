package com.shouqianba.trade.fund.settlement.application.biz.impl.withdraw;

import com.shouqianba.trade.fund.settlement.application.biz.BaseBiz;
import com.shouqianba.trade.fund.settlement.application.biz.context.impl.withdraw.WithdrawCreateContext;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.BrandSettleClient;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.brandsettle.model.res.SettlementWithdrawSubmitResult;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.model.SettlementStatisticsAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementstatistics.repository.SettlementStatisticsDomainRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 提现创建业务实现
 *
 * <AUTHOR> Date: 2025/6/15 Time: 10:20 AM
 */
@Slf4j
@Component
public class WithdrawCreateBiz extends BaseBiz<WithdrawCreateContext> {

    @Resource
    private BrandSettleClient brandSettleClient;
    @Resource
    private SettlementStatisticsDomainRepository settlementStatisticsDomainRepository;

    @Override
    protected void doBiz(WithdrawCreateContext context) {
        // 查询清分统计记录
        List<SettlementStatisticsAggrRoot> clearingStatistics =
                settlementStatisticsDomainRepository.batchQuery(context.genClearingStatisticsQuery());

        // 查询分账统计记录
        List<SettlementStatisticsAggrRoot> splitStatistics =
                settlementStatisticsDomainRepository.batchQuery(context.genSplitStatisticsQuery());

        // 计算清分统计金额
        Long clearingAmount = calculateTotalAmount(clearingStatistics);

        // 计算分账统计金额
        Long splitAmount = calculateTotalAmount(splitStatistics);
        if (clearingAmount > splitAmount && context.isWithdrawAllAmount()) {
            return;
        }
        // 调用品牌结算客户端提交提现申请
        SettlementWithdrawSubmitResult withdrawSubmitResult = 
                brandSettleClient.submitWithdraw(context.genWithdrawSubmitRequest());
        
        // 绑定提现结果到上下文
        context.bindWithdrawSubmitResult(withdrawSubmitResult);
        
        log.info("提现申请处理完成, result: {}", withdrawSubmitResult);
    }


    private Long calculateTotalAmount(List<SettlementStatisticsAggrRoot> statisticsList) {
        if (CollectionUtils.isEmpty(statisticsList)) {
            return 0L;
        }

        return statisticsList.stream()
                .filter(Objects::nonNull)
                .filter(stat -> Objects.nonNull(stat.getAmount()))
                .mapToLong(stat -> stat.getAmount().getTotalAmount())
                .sum();
    }
}