package com.shouqianba.trade.fund.settlement.application.biz.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.Lists;
import com.shouqianba.trade.fund.settlement.application.biz.config.CsvExportConfig;
import com.shouqianba.trade.fund.settlement.application.biz.model.csv.SettlementFlowCsvModel;
import com.shouqianba.trade.fund.settlement.common.exception.FundSettlementBizException;
import com.shouqianba.trade.fund.settlement.common.exception.enums.FundSettlementRespCodeEnum;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.SettlementFlowAggrRoot;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums.FlowTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

/**
 * <AUTHOR> Date: 2025/6/17 Time: 16:20 PM
 */
@Slf4j
@Service
public class CsvExportService {

    @Resource
    private CsvExportConfig csvExportConfig;

    /**
     * 流式导出结算流水数据到CSV文件
     *
     * @param dataProvider 数据提供者函数，支持分页查询
     * @param fileName 文件名（不含扩展名）
     * @return 生成的文件路径
     */
    public String exportSettlementFlowToCsv(Function<Integer, List<SettlementFlowAggrRoot>> dataProvider,
                                           String fileName) {

        String fullFileName = generateFileName(fileName);
        String filePath = csvExportConfig.getOutputDir() + File.separator + fullFileName;

        // 确保输出目录存在
        ensureOutputDirectoryExists();

        ExcelWriter excelWriter = null;
        try {
            log.info("[CSV导出]开始导出结算流水数据，文件路径: {}", filePath);

            // 创建Excel写入器
            excelWriter = EasyExcel.write(filePath, SettlementFlowCsvModel.class)
                    .registerWriteHandler(createCellStyleStrategy())
                    .build();

            WriteSheet writeSheet = EasyExcel.writerSheet("settlement_flow").build();

            int pageIndex = 0;
            int totalCount = 0;
            boolean hasMoreData = true;

            while (hasMoreData) {
                // 分批获取数据
                List<SettlementFlowAggrRoot> flowList = dataProvider.apply(pageIndex);

                if (flowList == null || flowList.isEmpty()) {
                    hasMoreData = false;
                    break;
                }

                // 转换为CSV模型
                List<SettlementFlowCsvModel> csvModels = convertToCsvModels(flowList);

                // 流式写入数据
                if (pageIndex == 0) {
                    // 第一批数据，写入表头
                    excelWriter.write(csvModels, writeSheet);
                } else {
                    // 后续数据，不写入表头
                    WriteTable writeTable = EasyExcel.writerTable().needHead(false).build();
                    excelWriter.write(csvModels, writeSheet, writeTable);
                }

                totalCount += csvModels.size();
                pageIndex++;

                log.info("[CSV导出]已处理第{}批数据，本批数量: {}，累计数量: {}",
                        pageIndex, csvModels.size(), totalCount);

                // 检查是否还有更多数据
                if (flowList.size() < csvExportConfig.getBatchSize()) {
                    hasMoreData = false;
                }

                // 内存管理：每处理一定数量的数据后，建议JVM进行垃圾回收
                if (pageIndex % 10 == 0) {
                    System.gc();
                }
            }

            log.info("[CSV导出]导出完成，总计导出{}条数据，文件路径: {}", totalCount, filePath);

            return filePath;

        } catch (Exception e) {
            log.error("[CSV导出]导出失败，文件路径: {}, 错误信息: {}", filePath, e.getMessage(), e);
            // 清理可能创建的文件
            cleanupFileOnError(filePath);
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SERVER_ERROR,
                    "CSV导出失败: " + e.getMessage());
        } finally {
            // 确保ExcelWriter被正确关闭
            closeExcelWriterSafely(excelWriter);
        }
    }

    /**
     * 转换结算流水数据为CSV模型
     */
    private List<SettlementFlowCsvModel> convertToCsvModels(List<SettlementFlowAggrRoot> flowList) {
        List<SettlementFlowCsvModel> csvModels = Lists.newArrayListWithCapacity(flowList.size());

        for (SettlementFlowAggrRoot flow : flowList) {
            SettlementFlowCsvModel csvModel = SettlementFlowCsvModel.builder()
                    .tranSn(flow.getTransSn())
                    .type(flow.getType().getCodeStr())
                    .build();
            csvModels.add(csvModel);
        }

        return csvModels;
    }

    /**
     * 转换流水类型枚举为字符串
     */
    private String convertFlowTypeToString(FlowTypeEnum flowType) {
        if (Objects.isNull(flowType)) {
            return "";
        }
        return flowType.getDesc();
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String baseName) {
        if (StringUtils.isBlank(baseName)) {
            baseName = csvExportConfig.getFileNamePrefix();
        }

        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return baseName + "_" + timestamp + ".csv";
    }

    /**
     * 确保输出目录存在
     */
    private void ensureOutputDirectoryExists() {
        try {
            File outputDir = new File(csvExportConfig.getOutputDir());
            if (!outputDir.exists()) {
                FileUtils.forceMkdir(outputDir);
                log.info("[CSV导出]创建输出目录: {}", csvExportConfig.getOutputDir());
            }
        } catch (IOException e) {
            log.error("[CSV导出]创建输出目录失败: {}", csvExportConfig.getOutputDir(), e);
            throw new FundSettlementBizException(FundSettlementRespCodeEnum.SERVER_ERROR,
                    "创建输出目录失败: " + e.getMessage());
        }
    }

    /**
     * 安全关闭ExcelWriter
     */
    private void closeExcelWriterSafely(ExcelWriter excelWriter) {
        if (excelWriter != null) {
            try {
                excelWriter.finish();
                log.debug("[CSV导出]ExcelWriter已安全关闭");
            } catch (Exception e) {
                log.warn("[CSV导出]关闭ExcelWriter时发生异常: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 错误时清理文件
     */
    private void cleanupFileOnError(String filePath) {
        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                if (deleted) {
                    log.info("[CSV导出]已清理错误文件: {}", filePath);
                } else {
                    log.warn("[CSV导出]清理错误文件失败: {}", filePath);
                }
            }
        } catch (Exception e) {
            log.warn("[CSV导出]清理错误文件时发生异常，文件路径: {}, 异常信息: {}", filePath, e.getMessage());
        }
    }

    /**
     * 创建单元格样式策略
     */
    private HorizontalCellStyleStrategy createCellStyleStrategy() {
        // 头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
