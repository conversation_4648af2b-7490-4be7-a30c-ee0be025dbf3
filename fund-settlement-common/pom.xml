<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shouqianba.trade</groupId>
        <artifactId>fund-settlement</artifactId>
        <version>1.0.5-SNAPSHOT</version>
    </parent>

    <artifactId>fund-settlement-common</artifactId>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!--一方库-->
        <dependency>
            <groupId>com.wosai.general</groupId>
            <artifactId>wosai-general-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.general</groupId>
            <artifactId>wosai-general-util</artifactId>
        </dependency>

        <!--二方库-->
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
        </dependency>

        <!--三方库-->
        <dependency>
            <groupId>io.confluent</groupId>
            <artifactId>kafka-avro-serializer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <!--maven install|deploy时，跳过本模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-install-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <configuration>
                    <enableCallerData>true</enableCallerData>
                    <patternLayout>%d{yyyy-MM-dd HH:mm:ss.SSS} %contextName [%thread] [%X{invoker}] [%tid] %-5level %logger{36}.%M - %msg%n</patternLayout>
                    <profiles>
                        <profile>
                            <name>prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 输出到标准输出，格式是JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_FILE_PATTERN</ref> <!-- 输出到文件，格式是格式化字符串 -->
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 -->
                            </references>
                        </profile>
                        <profile>
                            <name>dev</name>   <!-- 在本地开发调试时，在IDE中设置 active profile为dev -->
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 -->
                            </references>
                        </profile>
                    </profiles>
                    <scopes>
                        <scope>
                            <name>com.shouqianba.trade.fund.settlement</name>
                            <level>DEBUG</level>
                        </scope>
                        <scope>
                            <name>com.shouqianba.trade.fund.settlement.infrastructure.adapter.datasource.mysql</name>
                            <level>INFO</level>
                        </scope>
                        <scope>
                            <name>org.mybatis.spring</name>
                            <level>DEBUG</level>
                        </scope>
                        <scope>
                            <name>org.springframework.jdbc.datasource.DataSourceUtils</name>
                            <level>DEBUG</level>
                        </scope>
                        <scope>
                            <name>org.apache.kafka.clients.consumer.internals.ConsumerCoordinator</name>
                            <level>WARN</level>
                        </scope>
                        <scope>
                            <name>org.springframework.remoting.support</name>
                            <level>ERROR</level>
                        </scope>
                        <scope>
                            <name>com.googlecode.jsonrpc4j</name>
                            <level>ERROR</level>
                        </scope>
                        <scope>
                            <name>org.springframework.data.elasticsearch.core</name>
                            <level>DEBUG</level>
                        </scope>
                        <scope>
                            <name>org.springframework.data.elasticsearch.client.WIRE</name>
                            <level>TRACE</level>
                        </scope>
                    </scopes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
