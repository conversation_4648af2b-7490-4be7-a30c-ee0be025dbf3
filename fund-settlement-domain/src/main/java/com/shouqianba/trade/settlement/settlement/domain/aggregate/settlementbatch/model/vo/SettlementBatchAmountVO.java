package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

import javax.validation.constraints.NotNull;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class SettlementBatchAmountVO extends BaseVO<SettlementBatchAmountVO> {

    /**
     * 总金额（单位：分）
     */
    @NotNull(message = "总金额不能为空")
    private Long originAmount;

    /**
     * 手续费（单位：分）
     */
    private Long fee;

    /**
     * 结算金额（单位：分）
     */
//    @NotNull(message = "结算金额不能为空")
    private Long settleAmount;

    /**
     * 成功金额（单位：分）
     */
    private Long successAmount; // 冗余字段，todo跟踪用法

    /**
     * 失败金额（单位：分）
     */
    private Long failAmount;

    public static SettlementBatchAmountVO newEmptyInstance() {
        return SettlementBatchAmountVO
                .builder()
                .build();
    }

    @Override
    protected SettlementBatchAmountVO doReplaceNotNull(SettlementBatchAmountVO vo) {
        SettlementBatchAmountVOBuilder builder = toBuilder();

        if (vo.getOriginAmount() != null) {
            builder.originAmount(vo.getOriginAmount());
        }
        if (vo.getFee() != null) {
            builder.fee(vo.getFee());
        }

        if (vo.getSettleAmount() != null) {
            builder.settleAmount(vo.getSettleAmount());
        }
        if (vo.getSuccessAmount() != null) {
            builder.successAmount(vo.getSuccessAmount());
        }
        if (vo.getFailAmount() != null) {
            builder.failAmount(vo.getFailAmount());
        }

        return builder.build();
    }
}
