package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementbatch.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/***
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper = true)
public class SettlementBatchBizClearingResultInfoVO extends BaseVO<SettlementBatchBizClearingResultInfoVO> {

    // 清算文件OSS bucket
    private String ossBucketName;

    // 清算文件OSS路径
    private String ossFilePath;

    // 结果文件OSS路径
    private String resultOssFilePath;

    // 结果文件OSS bucket
    private String resultOssBucketName;

    // 清分状态
    private String statusDesc;

    // 清分完成时间
    private String finishTime;

    public static SettlementBatchBizClearingResultInfoVO newEmptyInstance() {
        return SettlementBatchBizClearingResultInfoVO
                .builder()
                .build();
    }

    @Override
    protected SettlementBatchBizClearingResultInfoVO doReplaceNotNull(SettlementBatchBizClearingResultInfoVO vo) {
        SettlementBatchBizClearingResultInfoVO.SettlementBatchBizClearingResultInfoVOBuilder builder = toBuilder();

        if (vo.getOssBucketName() != null) {
            builder.ossBucketName(vo.getOssBucketName());
        }

        if (vo.getOssFilePath() != null) {
            builder.ossFilePath(vo.getOssFilePath());
        }

        if (vo.getResultOssFilePath() != null) {
            builder.resultOssFilePath(vo.getResultOssFilePath());
        }

        if (vo.getResultOssBucketName() != null) {
            builder.resultOssBucketName(vo.getResultOssBucketName());
        }

        if (vo.getStatusDesc() != null) {
            builder.statusDesc(vo.getStatusDesc());
        }

        if (vo.getFinishTime() != null) {
            builder.finishTime(vo.getFinishTime());
        }

        return builder.build();
    }
}
