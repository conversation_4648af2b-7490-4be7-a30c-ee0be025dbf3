package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/***
 * 结算流水类型枚举
 * <AUTHOR> Date: 2025/5/19 Time: 4:34 PM
 */
@Getter
public enum FlowTypeEnum {
    
    ENTRY_SETTLEMENT((byte) 1, "入账结算"),
    SHARING_SETTLEMENT((byte) 2, "分账结算");

    private final byte code;
    private final String desc;

    FlowTypeEnum(byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonValue
    public byte getCode() {
        return code;
    }

    @JsonCreator
    public static FlowTypeEnum ofCode(Byte code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (FlowTypeEnum typeEnum : FlowTypeEnum.values()) {
            if (typeEnum.getCode() == code) {
                return typeEnum;
            }
        }
        return null;
    }

    public String getCodeStr() {
        return String.valueOf(code);
    }
}
