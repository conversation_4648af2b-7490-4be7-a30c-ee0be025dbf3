package com.shouqianba.trade.settlement.settlement.domain.aggregate.settlementflow.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.shouqianba.trade.fund.settlement.common.util.JsonUtils;
import com.shouqianba.trade.settlement.settlement.domain.aggregate.BaseVO;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.jackson.Jacksonized;

/***
 * <AUTHOR> Date: 2025/04/24 Time: 04:34 PM
 */
@Getter
@Jacksonized
@Builder(toBuilder = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SettlementFlowAmountVO extends BaseVO<SettlementFlowAmountVO> {

    /**
     * 结算金额（单位：分）
     */
    private Long originAmount;

    /**
     * 费用（单位：分）
     */
    private Long fee;
    
    /**
     * 结算金额（单位：分）
     */
    private Long settleAmount;

    /**
     * 转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJsonString() {
        return JsonUtils.toJsonString(this);
    }
    
    /**
     * 创建空实例
     *
     * @return 空实例
     */
    public static SettlementFlowAmountVO newEmptyInstance() {
        return SettlementFlowAmountVO.builder().build();
    }
    
    @Override
    protected SettlementFlowAmountVO doReplaceNotNull(SettlementFlowAmountVO vo) {
        SettlementFlowAmountVO.SettlementFlowAmountVOBuilder builder = toBuilder();

        if (vo.getOriginAmount() != null) {
            builder.originAmount(vo.getOriginAmount());
        }
        if (vo.getSettleAmount() != null) {
            builder.settleAmount(vo.getSettleAmount());
        }

        return builder.build();
    }
}
