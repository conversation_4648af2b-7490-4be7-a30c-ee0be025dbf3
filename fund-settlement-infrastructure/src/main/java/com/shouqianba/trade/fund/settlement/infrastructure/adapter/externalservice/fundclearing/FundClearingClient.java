package com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing;

import com.shouqianba.trade.fund.settlement.common.template.InvokeProcessor;
import com.shouqianba.trade.fund.settlement.common.template.InvokeTemplate;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.req.FundNotifyRequest;
import com.shouqianba.trade.fund.settlement.infrastructure.adapter.externalservice.fundclearing.model.res.FundNotifyResult;
import com.shouqianba.trade.fund.settlement.infrastructure.support.HttpClientFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> Date: 2023/6/17 Time: 9:41 PM
 */
@Slf4j
@Component
public class FundClearingClient {

    private static final String CONTENT_TYPE = "application/json;charset=utf-8";

    @Resource
    private HttpClientFacade httpClientFacade;

    @Value("${fund.clearing.notify.url:http://localhost:8080/fund-bill/notify/fund-notify}")
    private String fundNotifyUrl;

    /**
     * 发送资金清分通知
     *
     * @param request 资金清分通知请求
     * @return 资金清分通知结果
     */
    public FundNotifyResult fundNotify(FundNotifyRequest request) {
        return InvokeProcessor.process(request, new InvokeTemplate<>() {
            @Override
            protected FundNotifyResult invoke(FundNotifyRequest request) throws Throwable {
                log.info("[资金清分通知]>>>>>>入参: {}", request.toJsonString());

                String requestBody = request.toJsonString();
                HttpClientFacade.HttpResult httpResult = httpClientFacade.core()
                        .post(fundNotifyUrl, requestBody, CONTENT_TYPE);

                log.info("[资金清分通知]>>>>>>HTTP响应: code={}, body={}, timeConsuming={}ms",
                        httpResult.getCode(), httpResult.getBody(), httpResult.getTimeConsuming());

                FundNotifyResult result = FundNotifyResult.from(httpResult);
                log.info("[资金清分通知]>>>>>>出参: {}", result.getResult());

                return result;
            }

            @Override
            protected FundNotifyResult onFailure(FundNotifyRequest request, Throwable throwable) {
                log.error("[资金清分通知]>>>>>>调用异常, 请求: {}, 异常栈: ", request.toJsonString(), throwable);
                return FundNotifyResult.failure("调用异常: " + throwable.getMessage());
            }
        });
    }
}
