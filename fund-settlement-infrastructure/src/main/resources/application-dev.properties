kafka.consumer.enable=false
kafka.monitor.enable=false
server.port=8082
#数据源配置
spring.datasource.type=com.zaxxer.hikari.util.DriverDataSource
#核心数据源
spring.datasource.core.hikari.pool-name=core-pool
spring.datasource.core.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.core.hikari.jdbc-url=tk-fund-settlement-fund_settlement-0528?serverTimezone=Asia/Shanghai&socketTimeout=300000
spring.datasource.core.hikari.minimum-idle=1
spring.datasource.core.hikari.idle-timeout=300000
spring.datasource.core.hikari.maximum-pool-size=5
spring.datasource.core.hikari.connection-timeout=5000
spring.datasource.core.hikari.connection-test-query=select 1
spring.datasource.core.hikari.connection-init-sql=set names utf8mb4
#边缘数据源
spring.datasource.periphery.hikari.pool-name=periphery-pool
spring.datasource.periphery.hikari.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.periphery.hikari.jdbc-url=tk-fund-settlement-fund_settlement-0528?serverTimezone=Asia/Shanghai&socketTimeout=300000
spring.datasource.periphery.hikari.minimum-idle=1
spring.datasource.periphery.hikari.idle-timeout=300000
spring.datasource.periphery.hikari.maximum-pool-size=5
spring.datasource.periphery.hikari.connection-timeout=5000
spring.datasource.periphery.hikari.connection-test-query=select 1
spring.datasource.periphery.hikari.connection-init-sql=set names utf8mb4
#交易订单业务事件kafka生产者&消费者配置
spring.kafka.bootstrap-servers=***************:9092,***************:9092,***************:9092
spring.kafka.listener.type=batch
spring.kafka.producer.batch-size=16384
spring.kafka.producer.acks=1
spring.kafka.producer.retries=3
spring.kafka.producer.properties.linger.ms=50
spring.kafka.producer.properties.max.block.ms=5000
spring.kafka.producer.properties.enable.idempotence=false
spring.kafka.consumer.max-poll-records=500
spring.kafka.consumer.enable-auto-commit=true
spring.kafka.consumer.group-id=fund-settlement
spring.kafka.consumer.key-deserializer=org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
spring.kafka.consumer.value-deserializer=org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
spring.kafka.consumer.properties.spring.deserializer.key.delegate.class=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.properties.spring.deserializer.value.delegate.class=org.apache.kafka.common.serialization.StringDeserializer
#支付网关
spring.kafka.consumer.channel.trans.topic.acquiring-sync=trade
spring.kafka.consumer.channel.trans.group-id=fund-settlement-trans-consumer
spring.kafka.consumer.channel.trans.bootstrap-servers=192.168.101.89:9092,192.168.100.52:9092,192.168.101.90:9092
spring.kafka.consumer.channel.trans.max-poll-records=20
spring.kafka.consumer.channel.trans.enable-auto-commit=true
spring.kafka.consumer.channel.trans.schema-registry-url=http://192.168.101.89:8081,http://192.168.100.52:8081,http://192.168.101.90:8081
#品牌中心
spring.kafka.consumer.channel.brand.topic.brand-business=events_CUA_base-brand-business
spring.kafka.consumer.channel.brand.group-id=fund-settlement-consumer
spring.kafka.consumer.channel.brand.bootstrap-servers=***************:9092,***************:9092,***************:9092
spring.kafka.consumer.channel.brand.max-poll-records=20
spring.kafka.consumer.channel.brand.enable-auto-commit=true
spring.kafka.consumer.channel.brand.schema-registry-url=http://192.168.103.171:8081,http://192.168.103.172:8081,http://192.168.103.173:8081
#订单业务事件
spring.kafka.producer.topic.fund-settlement=events_TRADE_fund-settlement-event
spring.kafka.consumer.topic.fund-settlement=events_TRADE_fund-settlement-event
#飞书通知配置
feishu.notify.url=https://open.feishu.cn/open-apis/bot/v2/hook/84caf188-352d-4708-b087-44a3fe8de745
#商户中心
service.rpc.brand-business=http://brand-business.beta.iwosai.com
#资金核心
service.rpc.fund-core=http://fund-core.beta.iwosai.com
#抖音token获取
service.rpc.scorpio=http://scorpio.beta.iwosai.com
#core-business
service.rpc.core-business=http://core-business.beta.iwosai.com
#调整交易
service.rpc.trade-manage=http://trade-manage-service.beta.iwosai.com
#brand-settle
service.rpc.brand-settle=http://brand-settle-13800.beta.iwosai.com
#fund-clearing
fund.clearing.notify.url=http://fund-clearing.beta.iwosai.com/fund-bill/notify/fund-notify
