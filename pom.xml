<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.shouqianba.trade</groupId>
    <artifactId>fund-settlement</artifactId>
    <version>1.0.5-SNAPSHOT</version>
    <packaging>pom</packaging>
    <parent>
        <groupId>com.shouqianba.middleware</groupId>
        <artifactId>spring-boot-starter-parent-shouqianba</artifactId>
        <version>2.7.18-20250210</version>
    </parent>

    <modules>
        <module>fund-settlement-api</module>
        <module>fund-settlement-application</module>
        <module>fund-settlement-common</module>
        <module>fund-settlement-domain</module>
        <module>fund-settlement-infrastructure</module>
    </modules>

    <properties>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <wosai-general.version>2.0.5</wosai-general.version>
        <mysql.version>9.2.0</mysql.version>
        <okhttp.version>5.0.0-alpha.14</okhttp.version>
        <kotlin.version>1.9.20</kotlin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!--一方库-->
            <dependency>
                <groupId>com.shouqianba.trade</groupId>
                <artifactId>fund-settlement-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba.trade</groupId>
                <artifactId>fund-settlement-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba.trade</groupId>
                <artifactId>fund-settlement-application</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba.trade</groupId>
                <artifactId>fund-settlement-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba.trade</groupId>
                <artifactId>fund-settlement-infrastructure</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shouqianba.trade</groupId>
                <artifactId>fund-core-api</artifactId>
                <version>1.0.1</version>
            </dependency>

            <!--二方库-->
            <dependency>
                <groupId>com.wosai.trade</groupId>
                <artifactId>manage-api</artifactId>
                <version>1.11.70</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${shouqianba.apollo.verison}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>snakeyaml</artifactId>
                        <groupId>org.yaml</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.cua</groupId>
                <artifactId>brand-business-api</artifactId>
                <version>1.4.28</version>
                <exclusions>
                    <exclusion>
                        <groupId>xml-apis</groupId>
                        <artifactId>xml-apis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.upay</groupId>
                <artifactId>core-business-api</artifactId>
                <version>3.6.59</version>
                <exclusions>
                    <exclusion>
                        <artifactId>avro</artifactId>
                        <groupId>org.apache.avro</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.general</groupId>
                <artifactId>wosai-general-ds</artifactId>
                <version>${wosai-general.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.general</groupId>
                <artifactId>wosai-general-api</artifactId>
                <version>${wosai-general.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wosai.general</groupId>
                <artifactId>wosai-general-util</artifactId>
                <version>${wosai-general.version}</version>
            </dependency>

            <!--抖音 SDK-->
            <dependency>
                <groupId>com.wosai.authorize</groupId>
                <artifactId>scorpio-api</artifactId>
                <version>1.30.0</version>
                <exclusions>
                    <exclusion>
                        <artifactId>jsonrpc4j</artifactId>
                        <groupId>com.wosai.middleware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>ota-service-utils</artifactId>
                        <groupId>com.wosai.device</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-validation</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-web-rpc</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>httpcore-nio</artifactId>
                        <groupId>org.apache.httpcomponents</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>base64</artifactId>
                        <groupId>net.iharder</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>carrier</artifactId>
                        <groupId>com.wosai.middleware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-annotations</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-databind</artifactId>
                        <groupId>com.fasterxml.jackson.core</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-core</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-web</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-webmvc</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>mysql-connector-java</artifactId>
                        <groupId>mysql</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-beans</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-tx</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tomcat-juli</artifactId>
                        <groupId>org.apache.tomcat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-data</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-util</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-jdbc</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>tomcat-jdbc</artifactId>
                        <groupId>org.apache.tomcat</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-boot-starter-jdbc</artifactId>
                        <groupId>org.springframework.boot</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-jdbc</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>wosai-common-web</artifactId>
                        <groupId>com.wosai.pantheon</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-dbcp2</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-beanutils</artifactId>
                        <groupId>commons-beanutils</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>commons-collections</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-digester</artifactId>
                        <groupId>commons-digester</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>classmate</artifactId>
                        <groupId>com.fasterxml</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jboss-logging</artifactId>
                        <groupId>org.jboss.logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>validation-api</artifactId>
                        <groupId>javax.validation</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-validator</artifactId>
                        <groupId>commons-validator</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hibernate-validator</artifactId>
                        <groupId>org.hibernate.validator</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-aop</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-expression</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jodd-core</artifactId>
                        <groupId>org.jodd</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jodd-bean</artifactId>
                        <groupId>org.jodd</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wosai.upay</groupId>
                <artifactId>brand-settle-api</artifactId>
                <version>0.1.82-SNAPSHOT</version>
            </dependency>

            <!--三方库-->
            <dependency>
                <groupId>io.confluent</groupId>
                <artifactId>kafka-avro-serializer</artifactId>
                <version>6.0.2</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>4.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.17.0</version>
            </dependency>
            <dependency>
                <groupId>com.sun.xml.ws</groupId>
                <artifactId>jaxws-ri</artifactId>
                <version>4.0.0</version>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.10.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-vfs2</artifactId>
                <version>2.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId>
                <version>0.1.55</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-vfs2-jackrabbit1</artifactId>
                <version>2.10.0</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>3.9.0</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.30</version>
                <scope>provided</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-virtual-dev</name>
            <url>https://jfrog.wosai-inc.com/artifactory/maven-virtual-dev</url>
        </snapshotRepository>
    </distributionManagement>
</project>
